name: Build and Release DCST Tool

on:
  push:
    tags:
      - 'v*'
  workflow_dispatch:

permissions:
  contents: write  # necessario per creare la release

jobs:
  build:
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        os: [windows-latest, macos-latest, ubuntu-latest]

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.11'

      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install pyinstaller

      - name: Build with PyInstaller
        run: |
          ICON_ARG=""
          if [[ "${{ matrix.os }}" == "windows-latest" ]]; then
            ICON_ARG="--icon=icon.ico"
          elif [[ "${{ matrix.os }}" == "macos-latest" ]]; then
            ICON_ARG="--icon=icon.icns"
          fi
          pyinstaller --onefile --windowed $ICON_ARG --name DCST_Tool run.py

      - name: Rename output for release
        run: |
          if [[ "${{ matrix.os }}" == "windows-latest" ]]; then
            mv dist/DCST_Tool.exe DCST_Tool_Windows.exe
          elif [[ "${{ matrix.os }}" == "macos-latest" ]]; then
            mv dist/DCST_Tool DCST_Tool_macOS
          else
            mv dist/DCST_Tool DCST_Tool_Linux
          fi

      - name: Upload release asset
        uses: softprops/action-gh-release@v2
        with:
          files: |
            DCST_Tool_*
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
