#!/usr/bin/env python3
"""
Memory Profiler Integration Test for DCST Tool

This script tests the integration of memory_profiler for precise memory measurement
in the DCST Tool algorithms. It compares the old psutil-based measurements with
the new memory_profiler-based measurements.
"""

import sys
import os
import time
import logging

# Add the project directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def test_memory_profiler_availability():
    """Test if memory_profiler is available and working."""
    print("🔬 Testing memory_profiler availability...")
    
    try:
        from memory_profiler import memory_usage
        print("✅ memory_profiler imported successfully")
        
        # Test basic functionality
        def dummy_function():
            data = [i for i in range(100000)]
            return len(data)
        
        mem_usage = memory_usage((dummy_function, ()), interval=0.1)
        print(f"✅ memory_profiler test successful: {len(mem_usage)} measurements")
        print(f"   Memory range: {min(mem_usage):.1f} - {max(mem_usage):.1f} MB")
        return True
        
    except ImportError:
        print("❌ memory_profiler not available")
        return False
    except Exception as e:
        print(f"❌ memory_profiler test failed: {e}")
        return False

def test_dcst_memory_functions():
    """Test the DCST memory measurement functions."""
    print("\n🧪 Testing DCST memory measurement functions...")
    
    try:
        from app.algorithms import (
            get_memory_usage_precise,
            measure_algorithm_memory,
            MEMORY_PROFILER_AVAILABLE
        )
        
        print(f"✅ DCST memory functions imported successfully")
        print(f"   memory_profiler available: {MEMORY_PROFILER_AVAILABLE}")
        
        # Test basic memory measurement
        memory_kb = get_memory_usage_precise()
        print(f"✅ Current memory usage: {memory_kb:.1f} KB ({memory_kb/1024:.1f} MB)")
        
        # Test algorithm memory measurement
        def test_algorithm():
            # Simulate some memory-intensive work
            data = []
            for i in range(50000):
                data.append(f"test_string_{i}")
            return len(data)
        
        result, peak_memory, avg_memory = measure_algorithm_memory(test_algorithm)
        print(f"✅ Algorithm memory measurement successful:")
        print(f"   Result: {result}")
        print(f"   Peak memory: {peak_memory:.1f} KB ({peak_memory/1024:.1f} MB)")
        print(f"   Average memory: {avg_memory:.1f} KB ({avg_memory/1024:.1f} MB)")
        
        return True
        
    except ImportError as e:
        print(f"❌ Failed to import DCST memory functions: {e}")
        return False
    except Exception as e:
        print(f"❌ DCST memory function test failed: {e}")
        return False

def test_algorithm_memory_integration():
    """Test memory measurement integration with actual algorithms."""
    print("\n🚀 Testing algorithm memory integration...")
    
    try:
        from app.utils import generate_connected_random_graph
        from app.algorithms import (
            greedy_spanning_tree,
            measure_algorithm_memory,
            MEMORY_PROFILER_AVAILABLE
        )
        
        # Generate a test graph
        print("📊 Generating test graph...")
        G = generate_connected_random_graph(20, p=0.5)
        print(f"✅ Test graph created: {len(G.nodes())} nodes, {len(G.edges())} edges")
        
        # Test greedy algorithm with memory measurement
        print("🔍 Testing greedy algorithm with memory measurement...")
        
        def greedy_wrapper():
            return greedy_spanning_tree(G, max_children=3)
        
        start_time = time.time()
        tree, peak_memory, avg_memory = measure_algorithm_memory(greedy_wrapper)
        end_time = time.time()
        
        execution_time = end_time - start_time
        
        print(f"✅ Greedy algorithm memory measurement successful:")
        print(f"   Execution time: {execution_time:.3f} seconds")
        print(f"   Peak memory: {peak_memory:.1f} KB ({peak_memory/1024:.1f} MB)")
        print(f"   Average memory: {avg_memory:.1f} KB ({avg_memory/1024:.1f} MB)")
        print(f"   Tree nodes: {len(tree.nodes())}")
        print(f"   Tree edges: {len(tree.edges())}")
        print(f"   Memory profiler used: {MEMORY_PROFILER_AVAILABLE}")
        
        return True
        
    except Exception as e:
        print(f"❌ Algorithm memory integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def compare_memory_measurements():
    """Compare old psutil vs new memory_profiler measurements."""
    print("\n⚖️  Comparing memory measurement methods...")
    
    try:
        import psutil
        from app.algorithms import get_memory_usage_precise, MEMORY_PROFILER_AVAILABLE
        
        # Old psutil method
        def get_memory_psutil():
            import gc
            gc.collect()
            process = psutil.Process()
            memory_info = process.memory_info()
            return memory_info.rss / 1024  # in KB
        
        # Take multiple measurements
        measurements = []
        for i in range(5):
            psutil_memory = get_memory_psutil()
            profiler_memory = get_memory_usage_precise()
            
            measurements.append({
                'psutil': psutil_memory,
                'profiler': profiler_memory,
                'diff': abs(psutil_memory - profiler_memory)
            })
            
            time.sleep(0.1)  # Small delay between measurements
        
        print(f"📊 Memory measurement comparison ({len(measurements)} samples):")
        print("   Method      | Min (KB)  | Max (KB)  | Avg (KB)  | Std Dev")
        print("   ------------|-----------|-----------|-----------|----------")
        
        psutil_values = [m['psutil'] for m in measurements]
        profiler_values = [m['profiler'] for m in measurements]
        
        import statistics
        
        print(f"   psutil      | {min(psutil_values):8.1f} | {max(psutil_values):8.1f} | {statistics.mean(psutil_values):8.1f} | {statistics.stdev(psutil_values):8.1f}")
        print(f"   profiler    | {min(profiler_values):8.1f} | {max(profiler_values):8.1f} | {statistics.mean(profiler_values):8.1f} | {statistics.stdev(profiler_values):8.1f}")
        
        avg_diff = statistics.mean([m['diff'] for m in measurements])
        print(f"\n   Average difference: {avg_diff:.1f} KB ({avg_diff/1024:.1f} MB)")
        print(f"   memory_profiler available: {MEMORY_PROFILER_AVAILABLE}")
        
        return True
        
    except Exception as e:
        print(f"❌ Memory comparison test failed: {e}")
        return False

def main():
    """Main test function."""
    print("🎯 DCST Tool - Memory Profiler Integration Test")
    print("=" * 60)
    
    tests = [
        ("Memory Profiler Availability", test_memory_profiler_availability),
        ("DCST Memory Functions", test_dcst_memory_functions),
        ("Algorithm Memory Integration", test_algorithm_memory_integration),
        ("Memory Measurement Comparison", compare_memory_measurements)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            success = test_func()
            results.append((test_name, success))
        except Exception as e:
            print(f"❌ Test '{test_name}' crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print(f"\n{'='*60}")
    print("📋 TEST SUMMARY:")
    print("=" * 60)
    
    passed = 0
    for test_name, success in results:
        status = "✅ PASSED" if success else "❌ FAILED"
        print(f"   {status} - {test_name}")
        if success:
            passed += 1
    
    print(f"\n🎉 Results: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("🎊 All tests passed! Memory profiler integration is working correctly.")
    else:
        print("⚠️  Some tests failed. Check the output above for details.")
    
    return passed == len(results)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
