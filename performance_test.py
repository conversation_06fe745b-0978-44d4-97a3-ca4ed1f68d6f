#!/usr/bin/env python3
"""
Performance Test Script for DCST Tool Improvements

This script demonstrates the performance improvements made to the DCST Tool,
specifically focusing on:
1. Parallel image generation
2. Image caching
3. Async table generation
4. Batch processing

Run this script to see the performance differences between the old sequential
approach and the new optimized approach.
"""

import time
import os
import sys
import tempfile
import shutil

# Add the project directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.utils import (
    generate_connected_random_graph,
    draw_and_save_graph,
    save_table_as_image,
    batch_generate_images,
    clear_image_cache,
    get_performance_stats
)
import pandas as pd

def create_test_data():
    """Create test data for performance comparison."""
    print("🔧 Creazione dati di test...")

    # Generate test graphs
    graphs = []
    for i, size in enumerate([10, 15, 20]):
        G = generate_connected_random_graph(size, p=0.4)
        graphs.append((f"test_graph_{i}", G))

    # Generate test table data
    table_data = pd.DataFrame({
        'Istanza': ['<PERSON><PERSON><PERSON>', 'Media', 'Grande'] * 3,
        'Algoritmo': ['Greedy', 'Local', 'SA'] * 3,
        '<PERSON><PERSON>': [100, 85, 75, 150, 120, 95, 200, 180, 160],
        'Tempo (s)': [0.1, 0.5, 2.0, 0.2, 1.0, 4.0, 0.3, 2.0, 8.0],
        'Memoria (KB)': [1024, 2048, 3072, 1536, 3072, 4608, 2048, 4096, 6144],
        '<PERSON>zioni': [0, 0, 0, 1, 0, 0, 2, 1, 0],
        'Punteggio': [95, 88, 82, 85, 78, 75, 75, 68, 65]
    })

    return graphs, table_data

def test_sequential_approach(graphs, table_data, temp_dir):
    """Test the old sequential approach."""
    print("\n📊 Test approccio sequenziale (vecchio)...")
    start_time = time.time()

    # Sequential image generation
    for name, graph in graphs:
        filename = os.path.join(temp_dir, f"seq_{name}.png")
        draw_and_save_graph(graph, filename, max_children=3, is_spanning_tree=False)

    # Sequential table generation
    table_filename = os.path.join(temp_dir, "seq_table.png")
    save_table_as_image(table_data, table_filename)

    end_time = time.time()
    return end_time - start_time

def test_parallel_approach(graphs, table_data, temp_dir):
    """Test the new parallel approach."""
    print("\n🚀 Test approccio parallelo (nuovo)...")
    start_time = time.time()

    # Prepare batch tasks
    image_tasks = []
    for name, graph in graphs:
        filename = os.path.join(temp_dir, f"par_{name}.png")
        task = (draw_and_save_graph, (graph, filename), {"max_children": 3, "is_spanning_tree": False})
        image_tasks.append(task)

    # Batch image generation
    def progress_callback(completed, total):
        print(f"  📈 Progresso immagini: {completed}/{total}")

    batch_generate_images(image_tasks, progress_callback)

    # Async table generation
    table_filename = os.path.join(temp_dir, "par_table.png")
    table_future = save_table_as_image(table_data, table_filename, async_mode=True)
    table_future.result()  # Wait for completion

    end_time = time.time()
    return end_time - start_time

def test_caching_benefits(graphs, temp_dir):
    """Test the benefits of image caching."""
    print("\n💾 Test benefici del caching...")

    # Clear cache first
    clear_image_cache()

    # First generation (no cache)
    start_time = time.time()
    for i, (_, graph) in enumerate(graphs):
        filename = os.path.join(temp_dir, f"cache_test_{i}_first.png")
        draw_and_save_graph(graph, filename, max_children=3, is_spanning_tree=False)
    first_time = time.time() - start_time

    # Second generation (with cache - same graphs, different filenames)
    start_time = time.time()
    for i, (_, graph) in enumerate(graphs):
        filename = os.path.join(temp_dir, f"cache_test_{i}_second.png")
        draw_and_save_graph(graph, filename, max_children=3, is_spanning_tree=False)
    second_time = time.time() - start_time

    stats = get_performance_stats()
    print(f"  📊 Statistiche cache: {stats}")

    return first_time, second_time

def main():
    """Main performance test function."""
    print("🎯 DCST Tool - Test delle Prestazioni")
    print("=" * 50)

    # Create temporary directory for test files
    temp_dir = tempfile.mkdtemp(prefix="dcst_perf_test_")
    print(f"📁 Directory temporanea: {temp_dir}")

    try:
        # Create test data
        graphs, table_data = create_test_data()
        print(f"✅ Creati {len(graphs)} grafi di test e 1 tabella")

        # Test sequential approach
        seq_time = test_sequential_approach(graphs, table_data, temp_dir)
        print(f"⏱️  Tempo sequenziale: {seq_time:.2f} secondi")

        # Test parallel approach
        par_time = test_parallel_approach(graphs, table_data, temp_dir)
        print(f"⏱️  Tempo parallelo: {par_time:.2f} secondi")

        # Calculate improvement
        if seq_time > 0:
            improvement = ((seq_time - par_time) / seq_time) * 100
            speedup = seq_time / par_time if par_time > 0 else float('inf')
            print(f"\n🎉 RISULTATI:")
            print(f"   📈 Miglioramento: {improvement:.1f}%")
            print(f"   🚀 Speedup: {speedup:.2f}x")

        # Test caching benefits
        first_time, second_time = test_caching_benefits(graphs, temp_dir)
        print(f"\n💾 BENEFICI DEL CACHING:")
        print(f"   🔄 Prima generazione: {first_time:.2f}s")
        print(f"   ⚡ Seconda generazione: {second_time:.2f}s")
        if first_time > 0:
            cache_improvement = ((first_time - second_time) / first_time) * 100
            print(f"   📊 Miglioramento cache: {cache_improvement:.1f}%")

        # Final performance stats
        final_stats = get_performance_stats()
        print(f"\n📊 STATISTICHE FINALI:")
        print(f"   🗂️  Elementi in cache: {final_stats['cache_size']}")
        print(f"   ✅ Cache hits: {final_stats['cache_hits']}")
        print(f"   🔧 Thread pool attivo: {final_stats['thread_pool_active']}")
        print(f"   👥 Max workers: {final_stats['max_workers']}")

    except Exception as e:
        print(f"❌ Errore durante il test: {e}")
        import traceback
        traceback.print_exc()

    finally:
        # Cleanup
        try:
            shutil.rmtree(temp_dir)
            print(f"\n🧹 Directory temporanea rimossa: {temp_dir}")
        except Exception as e:
            print(f"⚠️  Errore nella pulizia: {e}")

        # Clear cache
        clear_image_cache()
        print("🧹 Cache pulita")

if __name__ == "__main__":
    main()
