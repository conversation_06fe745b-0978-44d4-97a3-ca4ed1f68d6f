DCST Tool Crash Report - 2025-05-31 12:36:49.227924
============================================================

Error: module 'tkinter' has no attribute 'scrolledtext'

Full Traceback:
----------------------------------------
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\backup\DCST_project_directory\project\run.py", line 120, in <module>
    app = App(root, progress_bar)
          ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\backup\DCST_project_directory\project\app\gui.py", line 95, in __init__
    self.log_text = tk.scrolledtext.ScrolledText(self.log_frame, height=10, bg="#1e1e1e", fg="#cccccc",
                    ^^^^^^^^^^^^^^^
AttributeError: module 'tkinter' has no attribute 'scrolledtext'

----------------------------------------

System Information:
CPU cores: 16
CPU usage: 7.5%
Total RAM: 31.9 GB
Available RAM: 24.2 GB
RAM usage: 24.2%
