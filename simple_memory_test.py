#!/usr/bin/env python3
"""
Simple test to verify memory_profiler integration works correctly.
"""

import sys
import os

# Add the project directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_basic_import():
    """Test basic import of memory profiler functionality."""
    try:
        print("Testing basic imports...")
        from app.algorithms import MEMORY_PROFILER_AVAILABLE, get_memory_usage_precise
        print(f"✅ MEMORY_PROFILER_AVAILABLE: {MEMORY_PROFILER_AVAILABLE}")
        
        memory_kb = get_memory_usage_precise()
        print(f"✅ Current memory usage: {memory_kb:.1f} KB ({memory_kb/1024:.1f} MB)")
        
        return True
    except Exception as e:
        print(f"❌ Import test failed: {e}")
        return False

def test_memory_measurement():
    """Test memory measurement functionality."""
    try:
        print("\nTesting memory measurement...")
        from app.algorithms import measure_algorithm_memory
        
        def test_function():
            # Create some data to use memory
            data = [i * 2 for i in range(10000)]
            return len(data)
        
        result, peak_memory, avg_memory = measure_algorithm_memory(test_function)
        print(f"✅ Algorithm result: {result}")
        print(f"✅ Peak memory: {peak_memory:.1f} KB ({peak_memory/1024:.1f} MB)")
        print(f"✅ Average memory: {avg_memory:.1f} KB ({avg_memory/1024:.1f} MB)")
        
        return True
    except Exception as e:
        print(f"❌ Memory measurement test failed: {e}")
        return False

def test_algorithm_integration():
    """Test integration with actual algorithms."""
    try:
        print("\nTesting algorithm integration...")
        from app.utils import generate_connected_random_graph
        from app.algorithms import greedy_spanning_tree, measure_algorithm_memory
        
        # Create a small test graph
        G = generate_connected_random_graph(10, p=0.6)
        print(f"✅ Test graph created: {len(G.nodes())} nodes, {len(G.edges())} edges")
        
        def greedy_test():
            return greedy_spanning_tree(G, max_children=3)
        
        tree, peak_memory, avg_memory = measure_algorithm_memory(greedy_test)
        print(f"✅ Greedy algorithm completed")
        print(f"✅ Tree has {len(tree.nodes())} nodes, {len(tree.edges())} edges")
        print(f"✅ Peak memory: {peak_memory:.1f} KB ({peak_memory/1024:.1f} MB)")
        print(f"✅ Average memory: {avg_memory:.1f} KB ({avg_memory/1024:.1f} MB)")
        
        return True
    except Exception as e:
        print(f"❌ Algorithm integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all tests."""
    print("🔬 Simple Memory Profiler Integration Test")
    print("=" * 50)
    
    tests = [
        ("Basic Import", test_basic_import),
        ("Memory Measurement", test_memory_measurement),
        ("Algorithm Integration", test_algorithm_integration)
    ]
    
    passed = 0
    for test_name, test_func in tests:
        print(f"\n--- {test_name} ---")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} PASSED")
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"❌ {test_name} CRASHED: {e}")
    
    print(f"\n{'='*50}")
    print(f"Results: {passed}/{len(tests)} tests passed")
    
    if passed == len(tests):
        print("🎉 All tests passed! Memory profiler integration is working.")
    else:
        print("⚠️ Some tests failed. Check the output above.")
    
    return passed == len(tests)

if __name__ == "__main__":
    success = main()
    print(f"\nTest {'SUCCESSFUL' if success else 'FAILED'}")
